package com.insurfact.ins.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Data Transfer Object for Advisor Profile information.
 * This DTO represents the complete advisor profile including personal information,
 * contact details, and associated metadata.
 * 
 * Used by the GET /api/v1/advisors/{advisorId} endpoint to return comprehensive
 * advisor information for the Personal Information tab in the UI.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Complete advisor profile information including personal details and contact information")
public class AdvisorProfileDTO {

    @Schema(description = "Unique advisor identifier", example = "12345")
    private Long advisorId;

    @Schema(description = "Advisor code/number", example = "ADV001")
    private String advisorCode;

    @Schema(description = "Current status of the advisor", example = "ACTIVE", allowableValues = {"ACTIVE", "INACTIVE", "PENDING", "SUSPENDED"})
    private String status;

    @Schema(description = "Type of advisor", example = "AGENT")
    private String advisorType;

    @Schema(description = "Date when advisor record was created")
    private LocalDateTime createdDate;

    @Schema(description = "Date when advisor record was last modified")
    private LocalDateTime lastModifiedDate;

    @Schema(description = "Personal contact information for the advisor")
    private ContactDTO contact;

    @Schema(description = "Additional notes or comments about the advisor")
    private String notes;

    @Schema(description = "Whether the advisor has an active login account")
    private Boolean hasLoginAccount;

    @Schema(description = "Username for login account (if exists)")
    private String username;

    @Schema(description = "Social Insurance Number (masked for security)", example = "***-***-123")
    private String sin;

    @Schema(description = "Corporate name if advisor operates under a corporation")
    private String corporateName;

    @Schema(description = "List of companies associated with the advisor")
    private List<CompanyDTO> companies;

    @Schema(description = "List of licenses held by the advisor")
    private List<LicenseDTO> licenses;

    // Manual builder in case Lombok doesn't work
    public static AdvisorProfileDTOBuilder builder() {
        return new AdvisorProfileDTOBuilder();
    }

    public static class AdvisorProfileDTOBuilder {
        private Long advisorId;
        private String advisorCode;
        private String status;
        private String advisorType;
        private LocalDateTime createdDate;
        private LocalDateTime lastModifiedDate;
        private ContactDTO contact;
        private String notes;
        private Boolean hasLoginAccount;
        private String username;
        private String sin;
        private String corporateName;
        private List<CompanyDTO> companies;
        private List<LicenseDTO> licenses;

        public AdvisorProfileDTOBuilder advisorId(Long advisorId) {
            this.advisorId = advisorId;
            return this;
        }

        public AdvisorProfileDTOBuilder advisorCode(String advisorCode) {
            this.advisorCode = advisorCode;
            return this;
        }

        public AdvisorProfileDTOBuilder status(String status) {
            this.status = status;
            return this;
        }

        public AdvisorProfileDTOBuilder advisorType(String advisorType) {
            this.advisorType = advisorType;
            return this;
        }

        public AdvisorProfileDTOBuilder createdDate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
            return this;
        }

        public AdvisorProfileDTOBuilder lastModifiedDate(LocalDateTime lastModifiedDate) {
            this.lastModifiedDate = lastModifiedDate;
            return this;
        }

        public AdvisorProfileDTOBuilder contact(ContactDTO contact) {
            this.contact = contact;
            return this;
        }

        public AdvisorProfileDTOBuilder notes(String notes) {
            this.notes = notes;
            return this;
        }

        public AdvisorProfileDTOBuilder hasLoginAccount(Boolean hasLoginAccount) {
            this.hasLoginAccount = hasLoginAccount;
            return this;
        }

        public AdvisorProfileDTOBuilder username(String username) {
            this.username = username;
            return this;
        }

        public AdvisorProfileDTOBuilder sin(String sin) {
            this.sin = sin;
            return this;
        }

        public AdvisorProfileDTOBuilder corporateName(String corporateName) {
            this.corporateName = corporateName;
            return this;
        }

        public AdvisorProfileDTOBuilder companies(List<CompanyDTO> companies) {
            this.companies = companies;
            return this;
        }

        public AdvisorProfileDTOBuilder licenses(List<LicenseDTO> licenses) {
            this.licenses = licenses;
            return this;
        }

        public AdvisorProfileDTO build() {
            AdvisorProfileDTO dto = new AdvisorProfileDTO();
            dto.advisorId = this.advisorId;
            dto.advisorCode = this.advisorCode;
            dto.status = this.status;
            dto.advisorType = this.advisorType;
            dto.createdDate = this.createdDate;
            dto.lastModifiedDate = this.lastModifiedDate;
            dto.contact = this.contact;
            dto.notes = this.notes;
            dto.hasLoginAccount = this.hasLoginAccount;
            dto.username = this.username;
            dto.sin = this.sin;
            dto.corporateName = this.corporateName;
            dto.companies = this.companies;
            dto.licenses = this.licenses;
            return dto;
        }
    }

    // Manual getter methods in case Lombok doesn't work
    public Long getAdvisorId() {
        return advisorId;
    }

    public String getAdvisorCode() {
        return advisorCode;
    }

    public ContactDTO getContact() {
        return contact;
    }
}
